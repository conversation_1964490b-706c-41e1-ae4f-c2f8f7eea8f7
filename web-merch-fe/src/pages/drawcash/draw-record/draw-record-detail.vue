<template>
  <view class="p-30rpx">
    <view class="rounded-lg bg-primary py-30rpx">
      <view class="text-28rpx">
        <wd-row :gutter="10">
          <wd-col :span="8" custom-class="col-flex text-#999">
            <text>通道商户编号</text>
            <text>通道商户名称</text>
            <text>提现时间</text>
            <text>提现流水单号</text>
            <text>交易笔数</text>
            <text>提现金额</text>
            <text>到账金额</text>
            <text>提现手续费金额</text>
            <text>结算周期</text>
            <text>提现状态</text>
            <text>银行名称</text>
            <text>银行支行名称</text>
            <text>银行卡号</text>
            <text v-if="detail.withdrawStatus === 3">
              失败说明
            </text>
          </wd-col>
          <wd-col :span="16" custom-class="col-flex items-end">
            <text>{{ detail.chnMerchNo || '--' }}</text>
            <text>{{ detail.chnMerchName || '--' }}</text>
            <text>{{ detail.createTime || '--' }}</text>
            <text>{{ detail.flowNo || '--' }}</text>
            <text>{{ detail.transCount || 0 }}</text>
            <text>{{ detail.withdrawAmount || 0 }}</text>
            <text>{{ detail.receivedAmount || 0 }}</text>
            <text>{{ detail.feeAmount || 0 }}</text>
            <text>{{ settlePeriodMap[detail.settlePeriod] || '--' }}</text>
            <text :class="receivedStatusMapColor[detail.withdrawStatus]">
              {{ receivedStatusMap[detail.withdrawStatus] || '--' }}
            </text>
            <text>{{ detail.bankName || '--' }}</text>
            <text>{{ detail.bankBranchName || '--' }}</text>
            <text>{{ detail.bankAccountNoMask || '--' }}</text>
            <text v-if="detail.withdrawStatus === 3">
              {{ detail.resDesc || '--' }}
            </text>
          </wd-col>
        </wd-row>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { NavigationHelper } from '@/utils';

const detail = ref<any>({});

const receivedStatusMap: EnumMap = {
  1: '出款中',
  2: '已到账',
  3: '出款失败',
  4: '处理中',
  5: '人工审核',
};
const receivedStatusMapColor: EnumMap = {
  1: 'text-blue',
  2: 'text-green',
  3: 'text-red',
  4: 'text-orange',
  5: 'text-yellow',
};

const settlePeriodMap: EnumMap = {
  1: 'D0提现',
  2: 'T1出款',
};
onLoad((query: any) => {
  const { transferredData } = NavigationHelper.getTransferredData(query);
  detail.value = Object.assign({}, transferredData);
});
</script>

<style lang="scss" scoped>
 :deep(.col-flex){
  @apply flex flex-col;

  text,view{
    @apply mb-20px break-all;
  }
}
</style>
