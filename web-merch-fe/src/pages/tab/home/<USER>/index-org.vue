<template>
  <view class="h-full">
    <page-paging ref="pagingRef" refresher-only @on-refresh="onRefreshData">
      <view class="banner">
        <view class="banner-bg" />
        <!-- 顶部 -->
        <view class="pos-relative">
          <!-- 导航栏 -->
          <wd-navbar custom-class="custom-navbar-class" :bordered="false" safe-area-inset-top>
            <!-- 个人信息 -->
            <template #title>
              <view class="px-30rpx text-white font-600">
                <view class="flex shrink-0 basis-1/2 items-center">
                  <i class="i-mdi-account-circle-outline size-60rpx" />
                  <text class="ml-5px text-34rpx">
                    {{ maskPhoneNumber(loginUser.account as string) }}
                  </text>
                </view>
              </view>
            </template>
          </wd-navbar>
        </view>

        <!-- 数据面板 -->
        <view class="px-30rpx pb-120rpx text-white">
          <swiper indicator-dots indicator-active-color="#ffffff">
            <swiper-item>
              <view class="swiper-pannel">
                <view class="pannel-block">
                  <view class="pannel-data">
                    <text>今日交易(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount(transInfo.todayTotalTransAmount) }}
                    </text>
                  </view>
                  <view class="pannel-data">
                    <text>昨日交易(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount(transInfo.lastDayTransAmt) }}
                    </text>
                  </view>
                </view>
                <view class="pannel-block">
                  <view class="pannel-data">
                    <text>本月交易(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount(transInfo.monthTotalTransAmount) }}
                    </text>
                  </view>
                  <view class="pannel-data">
                    <text>总交易(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount((transInfo.transTotalAmt)) }}
                    </text>
                  </view>
                </view>
              </view>
            </swiper-item>
            <swiper-item>
              <view class="swiper-pannel">
                <view class="pannel-block">
                  <view class="pannel-data">
                    <text>今日收益(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount(transInfo.todayTotalProfitAmount) }}
                    </text>
                  </view>
                  <view class="pannel-data">
                    <text>昨日收益(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount(transInfo.lastDayProfitAmt) }}
                    </text>
                  </view>
                </view>
                <view class="pannel-block">
                  <view class="pannel-data">
                    <text>本月收益(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount(transInfo.monthTotalProfitAmount) }}
                    </text>
                  </view>
                  <view class="pannel-data">
                    <text>总收益(元)</text>
                    <text class="pannel-data__num">
                      {{ formatAmount(transInfo.profitTotalAmt) }}
                    </text>
                  </view>
                </view>
              </view>
            </swiper-item>
          </swiper>
        </view>
      </view>

      <!-- 分润余额面板 -->
      <view class="pannel-class">
        <view class="flex items-center">
          <view class="flex grow flex-col items-start">
            <text>
              交易分润余额(元)
            </text>
            <text class="mt-5px text-30rpx font-bold">
              {{ formatAmount(profitInfo.profitBalance) }}
            </text>
          </view>
          <view class="ml-10px flex shrink-0 items-center" @click="handleToDrawRecord">
            <text class="text-#666">
              提现记录
            </text>
            <i class="i-mdi-chevron-right text-46rpx text-#666" />
          </view>
        </view>
        <view class="my-20rpx flex flex-col items-start">
          <text>活动分润余额(元)</text>
          <text class="mt-5px text-30rpx font-bold">
            {{ formatAmount(profitInfo.rewardBalance) }}
          </text>
        </view>

        <!-- 大区不支持小程序提现 -->
        <view v-if="loginUser.orgType !== 1" class="px-30rpx">
          <wd-button block @click="handleToDraw">
            提现
          </wd-button>
        </view>
      </view>

      <!-- 菜单 -->
      <view class="pos-relative mt-30rpx bg-transparent">
        <wd-grid :column="4" bg-color="transparent" clickable custom-class="custom-grid-class">
          <template v-for="(item, key) in menus" :key="key">
            <wd-grid-item v-if="!item.hidden" use-slot @itemclick="toMenuUrl(item)">
              <view class="flex flex-col items-center">
                <view class="icon-bg rounded-2xl p-12rpx">
                  <i class="size-80rpx text-white" :class="item.icon_class" />
                </view>
                <text class="mt-20rpx text-28rpx">
                  {{ item.menu_name }}
                </text>
              </view>
            </wd-grid-item>
          </template>
        </wd-grid>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import NP from 'number-precision';
import { useUserStore } from '@/store';
import { TransApi } from '@/api-org/trans';

defineOptions({
  options: {
    styleIsolation: 'shared', // 启用共享样式
  },
});

// 登录用户信息
const loginUser = computed(() => useUserStore().info);

const pagingRef = ref();

const transInfo: any = ref({});
const profitInfo: any = ref({
  profitBalance: '0.00',
  rewardBalance: '0.00',
});

// 菜单列表
interface IMenu {
  menu_name: string;
  icon_class: string;
  to: string; // 菜单跳转地址,
  hidden?: boolean; // 是否隐藏菜单项 默认不隐藏
};
const menus: IMenu[] = [
  {
    menu_name: '团队管理',
    icon_class: 'i-mdi-account-group-outline',
    to: '/pages-org/team/branch/index',
    hidden: loginUser.value.orgType !== 1,
  },
  {
    menu_name: '团队管理',
    icon_class: 'i-mdi-account-group-outline',
    to: '/pages-org/team/agent/index',
    hidden: loginUser.value.orgType === 1,
  },
  {
    menu_name: '商户管理',
    icon_class: 'i-mdi-account-outline',
    to: '/pages-org/merch/index',
  },
  {
    menu_name: '交易汇总',
    icon_class: 'i-mdi-data',
    to: '/pages-org/trans/index',
  },
  {
    menu_name: '政策管理',
    icon_class: 'i-mdi-relative-scale',
    to: '/pages-org/rate-policy/index',
  },
  {
    menu_name: '团队拓展',
    icon_class: 'i-mdi-account-arrow-up-outline',
    to: '/pages-org/extension-code/index',
    hidden: ![3, 5].includes(loginUser.value.orgType as number),
  },
  {
    menu_name: '商户报备',
    icon_class: 'i-mdi-account-check-outline',
    to: '/pages-org/merch-report/index',
  },
  {
    menu_name: '收益管理',
    icon_class: 'i-mdi-progress-star-four-points',
    to: '/pages-org/income/index',
  },
  {
    menu_name: '终端管理',
    icon_class: 'i-mdi-calculator',
    to: '/pages-org/terminal-manage/index',
    hidden: ![3, 5].includes(loginUser.value.orgType as number),
  },
];

onLoad(() => {
  onRefreshData();
});

function onRefreshData() {
  Promise.allSettled([
    querySelectTransProfitCount(),
    queryProfitRewardBalance(),
  ]).then(() => {
    pagingRef.value?.complete();
  });
}

async function querySelectTransProfitCount() {
  const data = await TransApi.selectTransProfitCount();
  transInfo.value = Object.assign({}, data);
}

async function queryProfitRewardBalance() {
  const data = await TransApi.profitRewardBalance();
  profitInfo.value = Object.assign(profitInfo.value, data);
}

// 手机号脱敏
function maskPhoneNumber(phoneNumber: string): string {
  // 检查手机号是否有效
  if (!/^\d{11}$/.test(phoneNumber)) {
    return phoneNumber;
  }

  // 对手机号进行脱敏处理
  const masked = phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');

  return masked;
}

/**
 * 金额转换 保留两位小数
 */
function formatAmount(amount: number) {
  if (!amount)
    return 0;

  amount = Number(amount);
  return NP.round(amount, 2);
}

function toMenuUrl({ to: url }: IMenu) {
  uni.navigateTo({ url });
}

// 提现
function handleToDraw() {
  uni.switchTab({ url: '/pages/tab/drawcash/index' });
}

// 提现记录
function handleToDrawRecord() {
  uni.navigateTo({ url: '/pages-org/drawcash/draw-record/index' });
}
</script>

<style lang="scss" scoped>
.banner {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.banner .banner-bg {
    position: absolute;
    left: -40%;
    z-index: 0;
    width: 180%;
    height: 100%;
    border-bottom-left-radius: 100%;
    border-bottom-right-radius: 100%;
    background-image: linear-gradient(0deg, #517cf0, #254bb2);  /* 渐变 */
    // background-color: #2bb3ed;
}

:deep(.custom-grid-class){
  .wd-grid-item{
    background-color: transparent !important;
  }

  .wd-grid-item__content{
    padding: 0;
    margin-bottom: 10px;
    background-color: transparent !important;
  }
}

.pannel-class{
  position: relative;

  @apply  mx-12px rounded-xl  p-14px shadow-md;

  margin-top: -100rpx;
  background-color: #fff;

}

.trans-progress-class{
  @apply  flex text-28rpx text-#999 ;

  .info-l,.info-r{
    @apply basis-1/2;
  }

  .info-r{
    @apply text-right;
  }

  .amount{
    @apply text-#333 text-28rpx;
  }
}

.swiper-pannel{
 .pannel-block{
  @apply flex items-start mt-10px;

 .pannel-data{
    @apply ml-30rpx flex w-50% flex-col items-start ;

   text{
      @apply text-30rpx;
    }

   .pannel-data__num{
      @apply mt-5px break-all text-30rpx font-500;
    }
  }
 }
}

.icon-bg{
  background: linear-gradient(to bottom right, #f6dd6a, #fc8f54);
}

:deep(.custom-navbar-class){
  background-color: transparent!important;

  .wd-navbar__left{
    padding: 0!important;
  }

  .wd-navbar__title{
  margin:0;
  max-width: 80%;
  color: white !important;
  }
}
</style>
